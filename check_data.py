import pandas as pd

# 读取数据
df = pd.read_csv('wildberries_data_20250801_010806.csv')

print("=" * 50)
print("数据收集统计报告")
print("=" * 50)

# 基本统计
print(f"总记录数: {len(df)} 条")

# 分类统计
positive = df[~df['keyword'].str.contains('排除', na=False)]
excluded = df[df['keyword'].str.contains('排除', na=False)]

print(f"正向关键词记录: {len(positive)} 条")
print(f"排除关键词记录: {len(excluded)} 条")

# 广告活动统计
print(f"涉及广告活动: {df['广告id'].nunique()} 个")
print(f"广告活动ID: {list(df['广告id'].unique())}")

# 关键词统计
print(f"正向关键词数量: {positive['keyword'].nunique()} 个")
print(f"排除关键词数量: {excluded['keyword'].nunique()} 个")

# 产品统计
print(f"涉及产品: {df['nm_id'].nunique()} 个")
print(f"产品ID: {list(df['nm_id'].unique())}")

print("\n" + "=" * 50)
print("正向关键词示例 (前10个):")
print("=" * 50)
print(positive[['广告id', 'keyword', 'views', 'clicks', 'ctr', 'count']].head(10).to_string(index=False))

print("\n" + "=" * 50)
print("数据字段完整性检查:")
print("=" * 50)
print(f"avg_similarity 非零记录: {len(df[df['avg_similarity'] > 0])} 条")
print(f"views 非零记录: {len(df[df['views'] > 0])} 条")
print(f"clicks 非零记录: {len(df[df['clicks'] > 0])} 条")
print(f"sum 非零记录: {len(df[df['sum'] > 0])} 条")
