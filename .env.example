# ==================== 数据库配置 ====================
# 选项1：使用主服务的PostgreSQL
# PG_HOST=postgres
# PG_PORT=5432
# PG_USER=admin
# PG_PASSWORD=admin123
# PG_DB=microservices

# 选项2：使用外部数据库
PG_HOST=************
PG_PORT=5432
PG_USER=lens
PG_PASSWORD=Ls.3956573
PG_DB=lens

# ==================== Wildberries API配置 ====================
# 多个API密钥用逗号分隔
WB_API_KEYS=your_api_key_1,your_api_key_2,your_api_key_3

# API基础URL
WB_ADV_API_BASE_URL=https://advert-api.wildberries.ru
WB_ANALYTICS_API_BASE_URL=https://seller-analytics-api.wildberries.ru

# ==================== 应用配置 ====================
# 日志级别 (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# 请求重试次数
MAX_RETRIES=3

# 请求超时时间（秒）
REQUEST_TIMEOUT=30

# 数据获取时间段（天）
DATA_PERIOD_DAYS=30

# 每次API调用间隔（秒）
API_CALL_INTERVAL=1
