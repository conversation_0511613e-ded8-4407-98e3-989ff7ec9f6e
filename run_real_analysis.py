"""
运行实际关键词数据分析（不使用测试数据）
"""
from loguru import logger
from main import WildberriesKeywordAnalyzer


def main():
    """运行实际数据分析"""
    logger.info("=" * 60)
    logger.info("运行Wildberries实际关键词数据分析")
    logger.info("=" * 60)
    
    analyzer = WildberriesKeywordAnalyzer()
    
    # 运行分析（处理所有拍卖广告）
    success = analyzer.run_analysis()
    
    if success:
        logger.info("🎉 实际数据分析成功完成！")
        logger.info(f"生成了 {len(analyzer.results)} 条分析结果")
        
        # 显示一些统计信息
        if analyzer.results:
            campaigns = set(r.campaign_id for r in analyzer.results)
            keywords = set(r.keyword for r in analyzer.results)
            
            logger.info("=" * 50)
            logger.info("📊 分析统计:")
            logger.info(f"  涉及广告活动: {len(campaigns)} 个")
            logger.info(f"  分析关键词: {len(keywords)} 个")
            logger.info(f"  总分析记录: {len(analyzer.results)} 条")
            
            # 显示前几个关键词
            logger.info("前10个关键词:")
            for i, result in enumerate(analyzer.results[:10], 1):
                logger.info(f"  {i}. 广告{result.campaign_id} - '{result.keyword}' (相似度: {result.avg_similarity}%)")
            
            logger.info("=" * 50)
    else:
        logger.warning("⚠️ 没有获取到实际关键词数据")
        logger.info("可能的原因:")
        logger.info("1. 广告活动没有设置正向关键词（只有排除关键词）")
        logger.info("2. 广告活动使用自动关键词匹配")
        logger.info("3. 关键词数据暂时为空")
        logger.info("")
        logger.info("建议:")
        logger.info("- 检查广告活动设置")
        logger.info("- 确认是否有正向关键词")
        logger.info("- 运行 python main.py 查看测试数据演示")


if __name__ == "__main__":
    main()
