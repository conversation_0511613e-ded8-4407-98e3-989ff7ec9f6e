"""
测试仅关键词获取功能
"""
from loguru import logger
from wb_api_client import WildberriesAPIClient
from config import settings


def test_keywords_only():
    """测试仅关键词获取"""
    logger.info("=" * 60)
    logger.info("测试关键词获取功能")
    logger.info("=" * 60)
    
    # 创建API客户端
    api_keys = settings.api_keys_list
    if not api_keys:
        logger.error("❌ 没有配置API密钥")
        return
    
    client = WildberriesAPIClient(api_keys[0])
    
    try:
        # 1. 获取广告活动
        logger.info("1. 获取广告活动列表...")
        campaigns = client.get_campaigns()
        logger.info(f"获取到 {len(campaigns)} 个广告活动")
        
        # 2. 筛选拍卖广告
        auction_campaigns = [c for c in campaigns if c.type == 9 and c.status == 9]
        logger.info(f"筛选出 {len(auction_campaigns)} 个拍卖广告")
        
        # 3. 测试前3个广告活动的关键词获取
        total_keywords = 0
        for i, campaign in enumerate(auction_campaigns[:3], 1):
            logger.info(f"\n--- 测试广告活动 {i}: {campaign.campaign_id} ---")
            
            try:
                # 获取关键词
                keywords = client.get_campaign_keywords(campaign.campaign_id)
                logger.info(f"✅ 获取到 {len(keywords)} 个关键词")
                
                # 显示前5个关键词
                for j, keyword in enumerate(keywords[:5], 1):
                    keyword_type = "排除" if keyword.keyword.startswith('[排除]') else "正向"
                    clean_keyword = keyword.keyword.replace('[排除]', '') if keyword.keyword.startswith('[排除]') else keyword.keyword
                    logger.info(f"  {j}. [{keyword_type}] '{clean_keyword}' (权重: {keyword.count})")
                
                total_keywords += len(keywords)
                
            except Exception as e:
                logger.error(f"❌ 获取广告活动 {campaign.campaign_id} 关键词失败: {e}")
        
        logger.info(f"\n📊 总结:")
        logger.info(f"  测试广告活动: {min(3, len(auction_campaigns))} 个")
        logger.info(f"  总关键词数: {total_keywords} 个")
        
        if total_keywords > 0:
            logger.info("✅ 关键词获取功能正常！")
            logger.info("💡 建议: 可以基于这些关键词数据进行分析")
        else:
            logger.warning("⚠️ 没有获取到关键词数据")
            logger.info("可能原因:")
            logger.info("1. 广告活动只设置了排除关键词")
            logger.info("2. 使用自动关键词匹配")
            logger.info("3. 关键词数据为空")
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")


if __name__ == "__main__":
    test_keywords_only()
