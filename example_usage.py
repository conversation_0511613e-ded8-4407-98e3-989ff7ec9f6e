"""
使用示例脚本
演示如何使用Wildberries广告关键词分析系统
"""
from loguru import logger
from main import WildberriesKeywordAnalyzer, create_test_data


def example_full_analysis():
    """完整分析示例"""
    logger.info("=" * 50)
    logger.info("完整分析示例")
    logger.info("=" * 50)
    
    analyzer = WildberriesKeywordAnalyzer()
    
    # 运行分析（处理所有拍卖广告）
    success = analyzer.run_analysis()
    
    if success:
        logger.info("✅ 分析成功完成！")
        logger.info(f"生成了 {len(analyzer.results)} 条分析结果")
    else:
        logger.warning("⚠️ 分析未能获取到实际数据，将使用测试数据演示")
        create_test_data()


def example_limited_analysis():
    """限制数量分析示例"""
    logger.info("=" * 50)
    logger.info("限制数量分析示例")
    logger.info("=" * 50)
    
    analyzer = WildberriesKeywordAnalyzer()
    
    # 只处理前5个拍卖广告
    success = analyzer.run_analysis(limit_campaigns=5)
    
    if success:
        logger.info("✅ 限制分析成功完成！")
        logger.info(f"生成了 {len(analyzer.results)} 条分析结果")
    else:
        logger.warning("⚠️ 分析未能获取到实际数据")


def example_test_data_only():
    """仅使用测试数据示例"""
    logger.info("=" * 50)
    logger.info("测试数据示例")
    logger.info("=" * 50)
    
    create_test_data()
    logger.info("✅ 测试数据生成完成！")


def main():
    """主函数 - 选择运行模式"""
    print("Wildberries广告关键词分析系统 - 使用示例")
    print("=" * 50)
    print("请选择运行模式:")
    print("1. 完整分析（处理所有拍卖广告）")
    print("2. 限制分析（处理前5个拍卖广告）")
    print("3. 仅生成测试数据")
    print("4. 退出")
    
    while True:
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            example_full_analysis()
            break
        elif choice == "2":
            example_limited_analysis()
            break
        elif choice == "3":
            example_test_data_only()
            break
        elif choice == "4":
            print("退出程序")
            break
        else:
            print("无效选择，请重新输入")


if __name__ == "__main__":
    main()
