"""
数据模型定义
"""
from typing import List, Optional, Dict, Any
from dataclasses import dataclass
from datetime import datetime


@dataclass
class Campaign:
    """广告活动模型"""
    campaign_id: int
    name: str
    type: int
    status: int
    create_time: datetime
    change_time: datetime


@dataclass
class Keyword:
    """关键词模型"""
    keyword: str
    count: int  # 热度


@dataclass
class KeywordStats:
    """关键词统计数据模型"""
    keyword: str
    views: int
    clicks: int
    sum: float  # 花费
    ctr: float  # 点击率


@dataclass
class Product:
    """产品模型"""
    nm_id: int
    product_id: Optional[str] = None


@dataclass
class SimilarityData:
    """相关度数据模型"""
    keyword: str
    target_product_id: str
    avg_similarity: float
    similar_count: int
    competitor_count: int
    valid_scores: int


@dataclass
class FinalResult:
    """最终结果模型"""
    campaign_id: int
    nm_id: int
    product_id: str
    keyword: str
    avg_similarity: float
    similar_count: int
    competitor_count: int
    valid_scores: int
    views: int
    sum: float
    clicks: int
    ctr: float
    count: int  # 关键词热度
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'campaign_id': self.campaign_id,
            'nm_id': self.nm_id,
            'product_id': self.product_id,
            'keyword': self.keyword,
            'avg_similarity': self.avg_similarity,
            'similar_count': self.similar_count,
            'competitor_count': self.competitor_count,
            'valid_scores': self.valid_scores,
            'views': self.views,
            'sum': self.sum,
            'clicks': self.clicks,
            'ctr': self.ctr,
            'count': self.count
        }
