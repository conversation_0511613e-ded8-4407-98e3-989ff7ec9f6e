"""
测试API和数据库连接
"""
import sys
from loguru import logger

from config import settings
from database import db_manager
from wb_api_client import WildberriesAPIClient


def test_database_connection():
    """测试数据库连接"""
    logger.info("=" * 50)
    logger.info("测试数据库连接")
    logger.info("=" * 50)
    
    # 测试连接
    if db_manager.test_connection():
        logger.info("✅ 数据库连接成功")
    else:
        logger.error("❌ 数据库连接失败")
        return False
    
    # 检查表是否存在
    if db_manager.check_table_exists():
        logger.info("✅ 相关度数据表存在")
        
        # 获取样本数据
        sample_data = db_manager.get_table_sample_data(3)
        if sample_data:
            logger.info(f"✅ 获取到 {len(sample_data)} 条样本数据:")
            for i, row in enumerate(sample_data, 1):
                logger.info(f"  样本 {i}: keyword='{row['keyword']}', product_id='{row['target_product_id']}', similarity={row['avg_similarity']}")
        else:
            logger.warning("⚠️ 表存在但没有数据")
    else:
        logger.error("❌ 相关度数据表不存在")
        return False
    
    return True


def test_api_connection():
    """测试API连接"""
    logger.info("=" * 50)
    logger.info("测试Wildberries API连接")
    logger.info("=" * 50)
    
    # 获取API密钥
    api_keys = settings.api_keys_list
    if not api_keys:
        logger.error("❌ 没有配置API密钥")
        return False
    
    logger.info(f"使用API密钥: {api_keys[0][:20]}...")
    
    # 创建API客户端
    client = WildberriesAPIClient(api_keys[0])
    
    try:
        # 测试获取广告活动
        logger.info("测试获取广告活动列表...")
        campaigns = client.get_campaigns()
        
        if campaigns:
            logger.info(f"✅ 成功获取 {len(campaigns)} 个广告活动")
            
            # 显示前几个广告活动信息
            for i, campaign in enumerate(campaigns[:3], 1):
                logger.info(f"  广告活动 {i}: ID={campaign.campaign_id}, 名称='{campaign.name}', 类型={campaign.type}, 状态={campaign.status}")
            
            # 筛选拍卖广告
            auction_campaigns = client.filter_auction_campaigns(campaigns)
            logger.info(f"✅ 筛选出 {len(auction_campaigns)} 个拍卖广告 (type=9, status=9)")
            
            if auction_campaigns:
                # 测试获取第一个拍卖广告的关键词
                test_campaign = auction_campaigns[0]
                logger.info(f"测试获取广告活动 {test_campaign.campaign_id} 的关键词...")
                
                try:
                    keywords = client.get_campaign_keywords(test_campaign.campaign_id)
                    logger.info(f"✅ 成功获取 {len(keywords)} 个关键词")
                    
                    # 显示前几个关键词
                    for i, keyword in enumerate(keywords[:5], 1):
                        logger.info(f"  关键词 {i}: '{keyword.keyword}' (热度: {keyword.count})")
                    
                except Exception as e:
                    logger.error(f"❌ 获取关键词失败: {e}")
                
                # 测试获取产品列表
                logger.info(f"测试获取广告活动 {test_campaign.campaign_id} 的产品列表...")
                try:
                    products = client.get_campaign_products(test_campaign.campaign_id)
                    logger.info(f"✅ 成功获取 {len(products)} 个产品")
                    
                    # 显示前几个产品
                    for i, product in enumerate(products[:5], 1):
                        logger.info(f"  产品 {i}: nm_id={product.nm_id}")
                        
                except Exception as e:
                    logger.error(f"❌ 获取产品列表失败: {e}")
            
            return True
        else:
            logger.warning("⚠️ 没有获取到广告活动")
            return True  # API连接正常，只是没有数据
            
    except Exception as e:
        logger.error(f"❌ API连接测试失败: {e}")
        return False


def test_integration():
    """测试集成功能"""
    logger.info("=" * 50)
    logger.info("测试集成功能")
    logger.info("=" * 50)
    
    # 获取一些测试数据
    sample_data = db_manager.get_table_sample_data(3)
    if not sample_data:
        logger.warning("⚠️ 没有样本数据，跳过集成测试")
        return True
    
    # 测试批量查询相关度数据
    test_pairs = []
    for row in sample_data:
        test_pairs.append((row['keyword'], row['target_product_id']))
    
    logger.info(f"测试批量查询 {len(test_pairs)} 个关键词-产品对的相关度数据...")
    similarity_data = db_manager.batch_get_similarity_data(test_pairs)
    
    if similarity_data:
        logger.info(f"✅ 成功获取 {len(similarity_data)} 条相关度数据")
        for (keyword, product_id), data in list(similarity_data.items())[:3]:
            logger.info(f"  '{keyword}' + '{product_id}': 相似度={data.avg_similarity:.3f}, 相似数量={data.similar_count}")
    else:
        logger.error("❌ 批量查询相关度数据失败")
        return False
    
    return True


def main():
    """主测试函数"""
    logger.info("开始连接测试...")
    
    # 显示配置信息
    logger.info(f"数据库配置: {settings.pg_host}:{settings.pg_port}/{settings.pg_db}")
    logger.info(f"API密钥数量: {len(settings.api_keys_list)}")
    
    success = True
    
    # 测试数据库连接
    if not test_database_connection():
        success = False
    
    # 测试API连接
    if not test_api_connection():
        success = False
    
    # 测试集成功能
    if not test_integration():
        success = False
    
    logger.info("=" * 50)
    if success:
        logger.info("🎉 所有测试通过！")
    else:
        logger.error("💥 部分测试失败！")
    logger.info("=" * 50)
    
    return success


if __name__ == "__main__":
    main()
