"""
Wildberries广告关键词效益分析主程序
"""
import json
from datetime import datetime
from typing import List, Dict, Any
from loguru import logger

from config import settings
from database import db_manager
from wb_api_client import MultiAccountAPIManager, WildberriesAPIClient
from data_processor import KeywordAnalyzer, DataExporter, KeywordEfficiencyAnalyzer
from models import Campaign, FinalResult


class WildberriesKeywordAnalyzer:
    """Wildberries关键词分析器主类"""
    
    def __init__(self):
        self.api_manager = MultiAccountAPIManager()
        self.results: List[FinalResult] = []
    
    def run_analysis(self, limit_campaigns: int = None) -> bool:
        """运行完整的分析流程"""
        logger.info("=" * 60)
        logger.info("开始Wildberries广告关键词效益分析")
        logger.info("=" * 60)
        
        try:
            # 1. 检查数据库连接
            if not self._check_database():
                return False
            
            # 2. 获取所有广告活动
            all_campaigns = self._get_campaigns()
            if not all_campaigns:
                logger.error("没有获取到任何广告活动")
                return False
            
            # 3. 筛选拍卖广告
            auction_campaigns = self._filter_auction_campaigns(all_campaigns)
            if not auction_campaigns:
                logger.error("没有找到符合条件的拍卖广告")
                return False
            
            # 4. 限制处理数量（用于测试）
            if limit_campaigns:
                auction_campaigns = auction_campaigns[:limit_campaigns]
                logger.info(f"限制处理前 {limit_campaigns} 个广告活动")
            
            # 5. 分析广告活动
            self.results = self._analyze_campaigns(auction_campaigns)
            if not self.results:
                logger.error("分析结果为空")
                return False
            
            # 6. 生成报告
            self._generate_reports()
            
            logger.info("=" * 60)
            logger.info("分析完成！")
            logger.info("=" * 60)
            return True
            
        except Exception as e:
            logger.error(f"分析过程中发生错误: {e}")
            return False
    
    def _check_database(self) -> bool:
        """检查数据库连接"""
        logger.info("检查数据库连接...")
        
        if not db_manager.test_connection():
            logger.error("数据库连接失败")
            return False
        
        if not db_manager.check_table_exists():
            logger.error("相关度数据表不存在")
            return False
        
        logger.info("✅ 数据库连接正常")
        return True
    
    def _get_campaigns(self) -> List[Campaign]:
        """获取所有广告活动"""
        logger.info("获取所有账号的广告活动...")
        
        campaigns = self.api_manager.get_all_campaigns()
        logger.info(f"✅ 总共获取到 {len(campaigns)} 个广告活动")
        
        return campaigns
    
    def _filter_auction_campaigns(self, campaigns: List[Campaign]) -> List[Campaign]:
        """筛选拍卖广告"""
        logger.info("筛选拍卖广告 (type=9, status=9)...")
        
        auction_campaigns = []
        for campaign in campaigns:
            if campaign.type == 9 and campaign.status == 9:
                auction_campaigns.append(campaign)
        
        logger.info(f"✅ 筛选出 {len(auction_campaigns)} 个拍卖广告")
        
        # 显示前几个广告活动信息
        for i, campaign in enumerate(auction_campaigns[:5], 1):
            logger.info(f"  拍卖广告 {i}: ID={campaign.campaign_id}, 名称='{campaign.name}', 创建时间={campaign.create_time}")
        
        return auction_campaigns
    
    def _analyze_campaigns(self, campaigns: List[Campaign]) -> List[FinalResult]:
        """分析广告活动"""
        logger.info(f"开始分析 {len(campaigns)} 个拍卖广告...")
        
        all_results = []
        
        # 使用第一个API客户端进行分析
        if not self.api_manager.clients:
            logger.error("没有可用的API客户端")
            return []
        
        analyzer = KeywordAnalyzer(self.api_manager.clients[0])
        
        for i, campaign in enumerate(campaigns, 1):
            logger.info(f"处理第 {i}/{len(campaigns)} 个广告活动: {campaign.campaign_id}")
            
            try:
                results = analyzer.analyze_campaign(campaign)
                all_results.extend(results)
                
                logger.info(f"  ✅ 广告活动 {campaign.campaign_id} 生成 {len(results)} 条结果")
                
            except Exception as e:
                logger.error(f"  ❌ 处理广告活动 {campaign.campaign_id} 失败: {e}")
                continue
        
        logger.info(f"✅ 分析完成，总共生成 {len(all_results)} 条结果")
        return all_results
    
    def _generate_reports(self):
        """生成报告"""
        logger.info("生成分析报告...")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 1. 导出原始数据
        csv_filename = f"keyword_analysis_{timestamp}.csv"
        excel_filename = f"keyword_analysis_{timestamp}.xlsx"
        
        DataExporter.to_csv(self.results, csv_filename)
        
        try:
            DataExporter.to_excel(self.results, excel_filename)
        except ImportError:
            logger.warning("未安装openpyxl，跳过Excel导出")
        
        # 2. 生成效益分析报告
        self._generate_efficiency_report(timestamp)
        
        # 3. 生成统计摘要
        self._generate_summary_report(timestamp)
    
    def _generate_efficiency_report(self, timestamp: str):
        """生成效益分析报告"""
        logger.info("生成效益分析报告...")
        
        # 分类关键词
        classified_results = KeywordEfficiencyAnalyzer.classify_keywords(self.results)
        
        # 生成操作建议
        recommendations = KeywordEfficiencyAnalyzer.generate_recommendations(classified_results)
        
        # 保存建议到JSON文件
        recommendations_filename = f"keyword_recommendations_{timestamp}.json"
        with open(recommendations_filename, 'w', encoding='utf-8') as f:
            json.dump(recommendations, f, ensure_ascii=False, indent=2)
        
        logger.info(f"✅ 效益分析报告已保存到 {recommendations_filename}")
        
        # 打印摘要
        logger.info("效益分析摘要:")
        logger.info(f"  保持不变关键词: {len(recommendations['keep_unchanged'])} 个")
        logger.info(f"  固定关键词: {len(recommendations['fix_keywords'])} 个")
        logger.info(f"  排除关键词: {len(recommendations['exclude_keywords'])} 个")
    
    def _generate_summary_report(self, timestamp: str):
        """生成统计摘要报告"""
        logger.info("生成统计摘要...")
        
        if not self.results:
            return
        
        # 计算统计数据
        total_campaigns = len(set(result.campaign_id for result in self.results))
        total_keywords = len(set(result.keyword for result in self.results))
        total_products = len(set(result.nm_id for result in self.results))
        
        total_views = sum(result.views for result in self.results)
        total_clicks = sum(result.clicks for result in self.results)
        total_spend = sum(result.sum for result in self.results)
        
        avg_ctr = sum(result.ctr for result in self.results) / len(self.results) if self.results else 0
        avg_similarity = sum(result.avg_similarity for result in self.results) / len(self.results) if self.results else 0
        
        summary = {
            "analysis_time": datetime.now().isoformat(),
            "total_records": len(self.results),
            "total_campaigns": total_campaigns,
            "total_keywords": total_keywords,
            "total_products": total_products,
            "total_views": total_views,
            "total_clicks": total_clicks,
            "total_spend": total_spend,
            "average_ctr": avg_ctr,
            "average_similarity": avg_similarity,
            "top_keywords_by_views": self._get_top_keywords_by_metric("views", 10),
            "top_keywords_by_clicks": self._get_top_keywords_by_metric("clicks", 10),
            "top_keywords_by_spend": self._get_top_keywords_by_metric("sum", 10)
        }
        
        summary_filename = f"analysis_summary_{timestamp}.json"
        with open(summary_filename, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        logger.info(f"✅ 统计摘要已保存到 {summary_filename}")
        
        # 打印关键统计
        logger.info("分析统计摘要:")
        logger.info(f"  总记录数: {len(self.results)}")
        logger.info(f"  涉及广告活动: {total_campaigns} 个")
        logger.info(f"  涉及关键词: {total_keywords} 个")
        logger.info(f"  涉及产品: {total_products} 个")
        logger.info(f"  总展示次数: {total_views:,}")
        logger.info(f"  总点击次数: {total_clicks:,}")
        logger.info(f"  总花费: {total_spend:.2f} 元")
        logger.info(f"  平均点击率: {avg_ctr:.2f}%")
        logger.info(f"  平均相似度: {avg_similarity:.1f}%")
    
    def _get_top_keywords_by_metric(self, metric: str, limit: int = 10) -> List[Dict[str, Any]]:
        """获取按指标排序的顶部关键词"""
        sorted_results = sorted(self.results, key=lambda x: getattr(x, metric), reverse=True)
        
        top_keywords = []
        for result in sorted_results[:limit]:
            top_keywords.append({
                "keyword": result.keyword,
                "campaign_id": result.campaign_id,
                "value": getattr(result, metric),
                "similarity": result.avg_similarity
            })
        
        return top_keywords


def create_test_data():
    """创建测试数据来演示系统功能"""
    logger.info("创建丰富的测试数据集...")

    from models import FinalResult
    import random

    # 模拟多个广告活动的关键词数据
    campaigns = [22590989, 26771183, 26795720, 26795937, 26796167]
    products = [253486273, 145678901, 987654321, 456789123, 789123456]

    # 俄语关键词库（照明相关）
    keywords_pool = [
        "светодиодные светильники на потолок", "люстра светодиодная", "плафон потолочный",
        "лампа настольная", "торшер напольный", "бра настенное", "подсветка led",
        "светильник подвесной", "люстра хрустальная", "лампочка энергосберегающая",
        "освещение кухни", "светильник для ванной", "уличный фонарь", "гирлянда новогодняя",
        "прожектор светодиодный", "лента светодиодная", "выключатель света", "диммер для ламп",
        "светильник офисный", "лампа для чтения", "ночник детский", "люстра в спальню",
        "освещение гостиной", "светильник в детскую", "подсветка мебели", "лампа галогенная",
        "светильник встраиваемый", "люстра классическая", "освещение рабочего места", "фонарик ручной"
    ]

    test_results = []

    # Генерируем данные для каждой комбинации кампания-продукт-ключевое слово
    for campaign_id in campaigns:
        for product_id in products:
            # Случайно выбираем 3-8 ключевых слов для каждой комбинации
            selected_keywords = random.sample(keywords_pool, random.randint(3, 8))

            for keyword in selected_keywords:
                # Генерируем реалистичные данные
                similarity = round(random.uniform(30.0, 95.0), 1)
                similar_count = random.randint(1, 25)
                competitor_count = random.randint(5, 50)
                valid_scores = random.randint(1, min(similar_count + 5, 30))

                views = random.randint(100, 5000)
                clicks = random.randint(5, int(views * 0.15))  # CTR обычно 1-15%
                spend = round(random.uniform(50.0, 500.0), 2)
                ctr = round((clicks / views) * 100, 2) if views > 0 else 0.0
                count = random.randint(5, 50)  # Популярность ключевого слова

                result = FinalResult(
                    campaign_id=campaign_id,
                    nm_id=product_id,
                    product_id=str(product_id),
                    keyword=keyword,
                    avg_similarity=similarity,
                    similar_count=similar_count,
                    competitor_count=competitor_count,
                    valid_scores=valid_scores,
                    views=views,
                    sum=spend,
                    clicks=clicks,
                    ctr=ctr,
                    count=count
                )
                test_results.append(result)

    logger.info(f"生成了 {len(test_results)} 条测试数据")

    # 生成报告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # 导出数据
    DataExporter.to_csv(test_results, f"test_keyword_analysis_{timestamp}.csv")

    # 生成效益分析
    classified_results = KeywordEfficiencyAnalyzer.classify_keywords(test_results)
    recommendations = KeywordEfficiencyAnalyzer.generate_recommendations(classified_results)

    with open(f"test_keyword_recommendations_{timestamp}.json", 'w', encoding='utf-8') as f:
        json.dump(recommendations, f, ensure_ascii=False, indent=2)

    logger.info("✅ 测试数据已生成")
    logger.info("效益分析摘要:")
    logger.info(f"  保持不变关键词: {len(recommendations['keep_unchanged'])} 个")
    logger.info(f"  固定关键词: {len(recommendations['fix_keywords'])} 个")
    logger.info(f"  排除关键词: {len(recommendations['exclude_keywords'])} 个")


def main():
    """主函数"""
    analyzer = WildberriesKeywordAnalyzer()

    # 运行分析（处理所有拍卖广告以找到有关键词的）
    success = analyzer.run_analysis()

    if success:
        logger.info("🎉 分析成功完成！")
    else:
        logger.error("💥 分析失败！")
        logger.info("尝试创建测试数据...")
        create_test_data()


if __name__ == "__main__":
    main()
